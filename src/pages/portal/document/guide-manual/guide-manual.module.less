.container {
  display: flex;
  justify-content: space-between;
  height: calc(100vh - 52px);

  .sideBar {
    width: 250px;
    background: #fff;

    .pageHead {
      height: 78px;
      line-height: 78px;
      font-size: 20px;
      font-weight: bold;
      padding: 0 20px;
      background: url('../assets/icon-release-log-sider-header.png') no-repeat;
      background-size: 100%;
      border-right: 1px solid #eee;
    }

    :global {
      .ant-menu {
        height: calc(100vh - 130px);
        color: #333;
        overflow-y: auto;
      }

      .ant-menu-item {
        margin-top: 0;
        margin-bottom: 0;

        &:hover {
          background: #f2f8fe;
        }
      }

      .ant-menu-item-selected {
        &::after {
          opacity: 1;
          transform: scaleY(1);
          border-right: 3px solid #128bed;
        }
      }
    }
  }

  :global {
    .doc {
      flex: 1;
      overflow-y: auto;
      padding: 10px;

      .content {
        background: #fff;
        padding: 0 15px 15px;
        min-height: calc(100vh - 72px);
        border-radius: 4px;

        h2 {
          margin-bottom: 5px;
          border-bottom: 1px solid #eee;
          padding: 20px 0;
          font-size: 24px;
          line-height: 32px;
        }

        h3 {
          padding-top: 10px;
          margin-bottom: 0;
          font-size: 16px;
          line-height: 32px;
        }

        p {
          padding-top: 10px;
          line-height: 22px;

          img {
            border-radius: 4px;
            border: 1px solid #eee;
            max-width: 100%;
            margin-bottom: 10px;
          }

          &:last-child {
            img {
              margin-bottom: 0;
            }
          }
        }

        .imgWrapper {
          padding-top: 0;
          margin-top: 10px;
          border-radius: 4px;
        }
      }
    }
  }
}
