import { defineComponent, computed, useContext, useAsync } from '@nuxtjs/composition-api';
import { Menu } from 'ant-design-vue';
import styles from './guide-manual.module.less';
import { MENUCONFIG } from './config/guide-manual.config';

const GuideManual = defineComponent({
  name: 'GuideManual',
  layout: 'embed',
  // middleware: 'auth',
  setup() {
    const { $content, error, params } = useContext();

    const selectedMenu = computed(() => [params.value.slug]);

    const manuals = useAsync(async () => {
      try {
        const content = await $content('manuals', params.value.slug).fetch<any>();

        return {
          content,
        };
      } catch (err) {
        error({
          statusCode: 404,
        });
      }
    }, params.value.slug);

    return { selectedMenu, manuals };
  },
  head: {
    title: '用户手册',
    titleTemplate: '%s - 第三方风险排查系统',
  },
  render() {
    return (
      <div class={styles.container}>
        <div class={styles.sideBar}>
          <div class={styles.pageHead}>用户手册</div>
          <Menu style={styles.munu} mode="inline" defaultSelectedKeys={this.selectedMenu}>
            {MENUCONFIG.map((menu) => {
              if (menu.children) {
                return [
                  <div slot="title">{menu.label}</div>,
                  <Menu.SubMenu key={menu.key} title={menu.label}>
                    {menu.children.map((child) => (
                      <Menu.Item key={child.key}>
                        <nuxt-link to={child.path}>{child.label}</nuxt-link>
                      </Menu.Item>
                    ))}
                  </Menu.SubMenu>
                ];
              }
              return (
                <Menu.Item key={menu.key}>
                  {menu.path ? <nuxt-link to={menu.path}>{menu.label}</nuxt-link> : menu.label}
                </Menu.Item>
              );
            })}
          </Menu>
        </div>
        <div class="doc">
          <div class="content">
            <nuxt-content document={this?.manuals} />
          </div>
          <div>
            最后更新于
          </div>
        </div>
      </div>
    );
  },
});
export default GuideManual;
