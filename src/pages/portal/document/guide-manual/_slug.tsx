import {defineComponent, computed, useContext} from "@nuxtjs/composition-api";
import { Menu } from 'ant-design-vue';
import styles from './guide-manual.module.less';
import { MENUCONFIG } from './config/guide-manual.config';

const GuideManual = defineComponent({
  name: 'GuideManual',
  layout: 'embed',
  // middleware: 'auth',
  setup() {
    const { params } = useContext();

    const selectedMenu = computed(() => [params.value.slug])
    return { selectedMenu };
  },
  async asyncData({$content, error, params}) {
    try {
      const manuals = await $content('manuals', params.slug).fetch<any>();

      return {
        manuals,
      };
    } catch (err) {
      error({
        statusCode: 404,
      });
    }
  },
  head: {
    title: '用户手册',
    titleTemplate: '%s - 第三方风险排查系统',
  },
  render() {
    return (
      <div class={styles.container}>
        <div class={styles.sideBar}>
          <div class={styles.pageHead}>用户手册</div>
          <Menu style={styles.munu} defaultSelectedKeys={this.selectedMenu}>
            {MENUCONFIG.map((menu) => {
              return <Menu.Item key={menu.key}>
                <nuxt-link to={menu.path}>{menu.label}</nuxt-link>
              </Menu.Item>;
            })}
          </Menu>
        </div>
        <div class="doc">
          <div class="content">
            <nuxt-content document={this?.manuals}/>
          </div>
        </div>
      </div>
    );
  },
});
export default GuideManual;
